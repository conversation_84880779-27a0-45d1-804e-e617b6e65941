<?php

/**
 * @author: xuz<PERSON>gy<PERSON>
 * @Time: 2024/9/3   23:20
 */

namespace app\zhanhui\controller\api;

use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\libraries\service\token\TokenService;
use app\user\ApiBaseController;
use app\zhanhui\logic\api\IndexLogic;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\zhanhui\request\api\IndexRequest;

class Index extends ApiBaseController
{

    protected $middleware = [
        'token' => [
            'except' => ['showConfigs', 'detail', 'bannerLists', 'showConfigs'],
        ],
        'must_login' => [
            'except' => ['showConfigs', 'detail', 'bannerLists', 'showConfigs'],
        ],
        // 'bind_mobile' => [],
        'signature' => [
            'except' => ['showConfigs', 'detail', 'bannerLists', 'showConfigs'],
        ],
    ];
    /**
     * 展会列表
     * @param IndexLogic $logic
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists(IndexLogic $logic)
    {
        return $logic->lists();
    }

    /**
     * 展会详情
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     */
    public function detail(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->detail($request->param());
    }

    /**
     * FAQ列表
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function faqLists(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->faqLists($request->param());
    }

    /**
     * banner列表
     * @param IndexLogic $logic
     * @return array
     */
    public function bannerLists(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->bannerLists($request->param());
    }

    /**
     * FAQ详情
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     */
    public function faqDetail(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->faqDetail($request->param());
    }


    /**
     * 显示配置
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return string[]
     */
    public function showConfigs(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->showConfigs($request->param());
    }

    /**
     * 用户获取展会留言消息列表
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     * @throws \app\libraries\exception\ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function messageBoardLists(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->messageBoardLists($request->param());
    }

    /**
     * 用户留言板发送聊天
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     */
    public function messageBoardCreate(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->messageBoardCreate($request->param());
    }

    /**
     * 展会管理员获取留言板留言列表
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     */
    public function adminMessageBoardLists(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->adminMessageBoardLists($request->param());
    }

    /**
     * 展会管理员获取留言对话列表
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     */
    public function adminMessageBoardChatLists(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->adminMessageBoardChatLists($request->param());
    }

    /**
     * 展会管理员回复留言
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     */
    public function adminMessageBoardChatReply(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->adminMessageBoardChatReply($request->param());
    }

    /**
     * 展会管理员更新留言板信息
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return array
     * @throws ApiException
     */
    public function adminUpdataMessageBoard(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->adminUpdataMessageBoard($request->param());
    }

    /**
     * 查询FAQ
     * @param IndexRequest $request
     * @param IndexLogic $logic
     * @return \app\zhanhui\models\MerchantZhanhuiFaqModel|array|mixed|\think\Model
     */
    public function queryFaq(IndexRequest $request, IndexLogic $logic)
    {
        return $logic->queryFaq($request->param());
    }
}
