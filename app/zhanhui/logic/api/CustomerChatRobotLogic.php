<?php

namespace app\zhanhui\logic\api;

use app\libraries\exception\ApiException;
use app\libraries\service\ai_workflow\prompt\PromptBuildService;
use app\libraries\service\ai_workflow\tools\ZhipuTool;
use app\libraries\service\token\TokenService;
use app\square\logic\api\ChatLogic;
use app\user\models\UsersModel;
use app\zhanhui\models\MerchantCustomerChatRobotModel;
use app\zhanhui\models\MerchantCustomerChatRobotRecordModel;
use app\zhanhui\models\MerchantCustomerChatRobotReplyRecordModel;
use app\zhanhui\models\MerchantCustomerChatRobotRoleModel;
use app\zhanhui\models\MerchantCustomerChatRobotTaskModel;
use app\zhanhui\models\MerchantCustomerChatRobotSendMessageModel;
use app\zhanhui\models\MerchantCustomerChatRobotZhanhuiModel;
use app\zhanhui\job\CustomerChatRobotTaskJob;
use think\facade\Queue;
use app\zhanhui\models\MerchantZhanhuiConfigModel;
use app\zhanhui\models\MerchantZhanhuiModel;
use app\zhanhui\cache\ZhanhuiCache;
use app\merchant\models\MerchantModel;
use app\merchant\models\MerchantKnowledgeBaseModel;
use app\constdir\SysErrorCode;
use app\libraries\utils\lock\RedisLock;

class CustomerChatRobotLogic
{
    /**
     * 注册客服机器人
     * @param array $params
     * @return array
     * @throws ApiException
     */
    public function register(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_register_' . $params['merchantGuid'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 3);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        // 判断商家是否存在
        $merchant = MerchantModel::where('guid', $params['merchantGuid'])->findOrEmpty();
        if ($merchant->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '商户不存在');
        }

        try {
            // 验证必要参数
            if (empty($params['deviceId'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '设备ID不能为空');
            }
            if (empty($params['otherPlatformUserUuid'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台用户UUID不能为空');
            }

            // 根据唯一条件查找是否存在
            $model = MerchantCustomerChatRobotModel::where([
                'device_id' => $params['deviceId'],
                'robot_type' => $params['robotType'],
                'merchant_guid' => $params['merchantGuid'],
                'other_platform_user_uuid' => $params['otherPlatformUserUuid']
            ])->findOrEmpty();

            if ($model->isEmpty()) {
                // 不存在则创建新记录
                $model = new MerchantCustomerChatRobotModel();
            }

            // 更新或创建记录
            $model->merchantGuid = $params['merchantGuid'];
            $model->robotType = $params['robotType'];
            $model->deviceId = $params['deviceId'];
            $model->robotName = $params['otherPlatformUserNickname'] ??
                ($merchant['merchantName'] . '-' . MerchantCustomerChatRobotModel::$robotTypeMap[$params['robotType']] . '机器人');
            $model->robotAvatar = $params['robotAvatar'] ?? '';
            $model->robotDesc = $params['robotDesc'] ?? '';
            $model->otherPlatformUserUuid = $params['otherPlatformUserUuid'];
            $model->otherPlatformUserNickname = $params['otherPlatformUserNickname'] ?? '';
            $model->otherPlatformUserAvatar = $params['otherPlatformUserAvatar'] ?? '';
            $model->clientRobotName = $params['clientRobotName'] ?? '';
            $model->extendConfig = $params['extendConfig'] ?? null;

            if (!$model->save()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '操作失败');
            }

            return [
                'guid' => $model->guid,
                'robotName' => $model->robotName,
            ];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }

    /**
     * 更新客服机器人聊天记录
     * @param array $params
     * @return array
     * @throws ApiException
     */
    public function updateChatRecord(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_update_record_' . $params['merchantGuid'] . '_' . $params['deviceId'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 3);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        try {
            // 验证必要参数
            if (empty($params['deviceId'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '设备ID不能为空');
            }
            if (empty($params['otherPlatformUserUuid'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台用户UUID不能为空');
            }

            // 判断机器人是否存在
            $robot = MerchantCustomerChatRobotModel::where([
                'device_id' => $params['deviceId'],
                'robot_type' => $params['robotType'],
                'merchant_guid' => $params['merchantGuid'],
                'other_platform_user_uuid' => $params['otherPlatformUserUuid']
            ])->findOrEmpty();

            if ($robot->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
            }

            // 查找或创建聊天记录
            $record = MerchantCustomerChatRobotRecordModel::where('robot_guid', $robot->guid)
                ->where('other_platform_chat_uuid', $params['otherPlatformChatUuid'])
                ->findOrEmpty();

            if ($record->isEmpty()) {
                $record = new MerchantCustomerChatRobotRecordModel();
                $record->merchant_guid = $params['merchantGuid'];
                $record->robot_guid = $robot->guid;
                $record->other_platform_chat_uuid = $params['otherPlatformChatUuid'];
            }

            $record->chat_title = $params['chatTitle'] ?? '';
            $record->chat_content = $params['chatContent'];

            if (!$record->save()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '更新聊天记录失败');
            }

            return [
                'guid' => $record->guid,
                'otherPlatformChatUuid' => $record->other_platform_chat_uuid,
            ];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }

    /**
     * 回复客服机器人聊天
     * @param array $params
     * @return array
     * @throws ApiException
     */
    public function replyChat(array $params)
    {
        LogInfo('customer_chat_robot_reply', '客服机器人回复', '开始回复', $params);
        if (empty($params['askQuestion'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '问题不能为空');
        }
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_reply_' . $params['merchantGuid'] . '_' . $params['otherPlatformUserUuid'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 60);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }
        try {
            // 验证必要参数
            if (empty($params['deviceId'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '设备ID不能为空');
            }
            if (empty($params['otherPlatformUserUuid'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台用户UUID不能为空');
            }

            // 判断机器人是否存在
            $robot = MerchantCustomerChatRobotModel::where([
                'device_id' => $params['deviceId'],
                'robot_type' => $params['robotType'],
                'merchant_guid' => $params['merchantGuid'],
                'other_platform_user_uuid' => $params['otherPlatformUserUuid']
            ])->findOrEmpty();

            if ($robot->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
            }

            // 调用AI接口获取回复内容
            $aiReply = [
                'type' => 'text',
                'content' => ''
            ];
            // 获取当前机器人绑定的知识库，并执行向量检索获得关联内容
            $knowledgeData = $this->getRobotKnowledgeData($robot, $params['askQuestion']);

            // 构建提示词
            $customerChatPrompt = PromptBuildService::getInstance()
                ->customerServiceAssistant(
                    json_encode($params['chatContent'], JSON_UNESCAPED_UNICODE),
                    json_encode($knowledgeData, JSON_UNESCAPED_UNICODE),
                );

            $aiReply['content'] = ZhipuTool::getInstance()->completeText(
                $params['askQuestion'],
                'text',
                false,
                $customerChatPrompt
            );

            // 验证回复内容类型
            if (
                !in_array($aiReply['type'], [
                MerchantCustomerChatRobotReplyRecordModel::REPLY_CONTENT_TYPE_TEXT,
                MerchantCustomerChatRobotReplyRecordModel::REPLY_CONTENT_TYPE_IMAGE,
                MerchantCustomerChatRobotReplyRecordModel::REPLY_CONTENT_TYPE_VIDEO,
                MerchantCustomerChatRobotReplyRecordModel::REPLY_CONTENT_TYPE_FILE,
                MerchantCustomerChatRobotReplyRecordModel::REPLY_CONTENT_TYPE_JOB
                ])
            ) {
                throwException(SysErrorCode::SYS_ERROR_CODE, 'AI回复内容类型不正确');
            }
            // 去除回答中的\n换行符
            $aiReply['content'] = str_replace("\n", '', $aiReply['content']);

            // 保存回复记录
            $record = new MerchantCustomerChatRobotReplyRecordModel();
            $record->merchantGuid = $params['merchantGuid'];
            $record->robotGuid = $robot->guid;
            $record->otherPlatformChatUuid = $params['otherPlatformChatUuid'];
            $record->askQuestion = $params['askQuestion'];
            $record->chatTitle = $params['chatTitle'] ?? '';
            $record->chatContent = $params['chatContent'];
            $record->replyContentType = $aiReply['type'];
            $record->replyContent = $aiReply['content'];

            if (!$record->save()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '保存回复记录失败');
            }

            return [
                'guid' => $record->guid,
                'replyContentType' => $record->replyContentType,
                'replyContent' => $record->replyContent,
            ];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            //ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }


    /**
     * 获取机器人绑定的知识库数据
     * @param $robot
     * @param $askQuestion
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getRobotKnowledgeData($robot, $askQuestion)
    {
        $buildZhanhui = MerchantCustomerChatRobotZhanhuiModel::getInstance()
            ->where('robot_guid', $robot->guid)
            ->select()
            ->toArray();
        $zhanhuiGuids = array_column($buildZhanhui, 'zhanhuiGuid');
        if (!$zhanhuiGuids) {
            return [];
        }
        // 获取知识库列表
        $knowledgeGuids = MerchantZhanhuiConfigModel::getInstance()
            ->whereIn('zhanhui_guid', $zhanhuiGuids)
            ->column('merchant_knowledge_guid');
        if (!$knowledgeGuids) {
            return [];
        }
        // 提取知识库内容
        $chatLogic = new ChatLogic();
        $knowledgeData = [];
        foreach ($knowledgeGuids as $knowledgeGuid) {
            $knowledgeData[] = $chatLogic->loadingMerchantKnowledgeContent($knowledgeGuid, $askQuestion);
        }
        return $knowledgeData;
    }


    /**
     * 客服机器人列表
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function lists()
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }
        $merchantGuid = $userInfo['merchantGuid'];

        // 获取机器人列表
        $list = MerchantCustomerChatRobotModel::getInstance()
            ->where('merchant_guid', $merchantGuid)
            ->select()->toArray();

        // 获取每个机器人绑定的展会列表
        foreach ($list as &$robot) {
            // 获取机器人绑定的展会关系
            $robotZhanhuiList = MerchantCustomerChatRobotZhanhuiModel::getInstance()
                ->where('robot_guid', $robot['guid'])
                ->select()
                ->toArray();

            $zhanhuiGuids = array_column($robotZhanhuiList, 'zhanhuiGuid');
            $zhanhuiList = [];

            if (!empty($zhanhuiGuids)) {
                // 获取展会详细信息
                $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
                    ->whereIn('guid', $zhanhuiGuids)
                    ->field('guid,name')
                    ->select()
                    ->toArray();

                foreach ($zhanhuiInfo as $zhanhui) {
                    $zhanhuiList[] = [
                        'guid' => $zhanhui['guid'],
                        'name' => $zhanhui['name'],
                    ];
                }
            }

            $robot['zhanhuiList'] = $zhanhuiList;
        }

        return $list;
    }

    /**
     * 客服机器人展会批量分配
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function assignZhanhui(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_assign_zhanhui_' . $params['robotGuid'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 5);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        try {
            // 检查机器人是否存在
            $robot = MerchantCustomerChatRobotModel::getInstance()
                ->where('guid', $params['robotGuid'])
                ->findOrEmpty();
            if ($robot->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
            }
            // 检查商家权限
            $userId = TokenService::getInstance()->getTokenEntity()->userId;
            $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
            if ($userInfo->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
            }
            // 开始事务
            MerchantCustomerChatRobotZhanhuiModel::getInstance()->startTrans();

            try {
                // 1. 首先删除该机器人的所有展会绑定
                MerchantCustomerChatRobotZhanhuiModel::destroy(function ($query) use ($params) {
                    $query->where('robot_guid', $params['robotGuid']);
                });

                // 2. 如果提供了新的展会列表，则添加新的绑定关系
                if (!empty($params['zhanhuiGuids'])) {
                    // 验证展会列表中的展会是否存在且属于同一商家
                    $zhanhuiList = MerchantZhanhuiModel::getInstance()
                        ->whereIn('guid', $params['zhanhuiGuids'])
                        ->where('merchant_guid', $robot['merchant_guid'])
                        ->select()
                        ->toArray();

                    // 提取有效的展会 GUID
                    $validZhanhuiGuids = array_column($zhanhuiList, 'guid');

                    // 添加新的绑定关系
                    foreach ($validZhanhuiGuids as $zhanhuiGuid) {
                        $relation = new MerchantCustomerChatRobotZhanhuiModel();
                        $relation->merchantGuid = $robot['merchant_guid'];
                        $relation->robotGuid = $params['robotGuid'];
                        $relation->zhanhuiGuid = $zhanhuiGuid;
                        $relation->save();
                    }
                }

                // 提交事务
                MerchantCustomerChatRobotZhanhuiModel::getInstance()->commit();

                return ['message' => '机器人展会分配成功'];
            } catch (\Exception $e) {
                // 回滚事务
                MerchantCustomerChatRobotZhanhuiModel::getInstance()->rollback();
                throwException(SysErrorCode::SYS_ERROR_CODE, '分配展会失败: ' . $e->getMessage());
            }
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }

        return [];
    }

    /**
     * 获取机器人角色列表
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function roleList(array $params)
    {
        // 检查机器人是否存在
        $robot = MerchantCustomerChatRobotModel::getInstance()
            ->where('guid', $params['robotGuid'])
            ->findOrEmpty();
        if ($robot->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
        }

        // 获取当前用户信息
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 检查商家权限
        if ($userInfo['merchantGuid'] !== $robot['merchant_guid']) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您没有权限操作该机器人');
        }

        // 获取角色列表
        $list = MerchantCustomerChatRobotRoleModel::getInstance()
            ->where('robot_guid', $params['robotGuid'])
            ->select()
            ->toArray();

        // 获取所有自定义知识库的GUID列表
        $allCustomKnowledgeGuids = [];
        foreach ($list as $role) {
            if ($role['knowledgeBindType'] === 'custom' && !empty($role['customKnowledgeGuids'])) {
                $allCustomKnowledgeGuids = array_merge($allCustomKnowledgeGuids, $role['customKnowledgeGuids']);
            }
        }

        // 如果有自定义知识库，则查询知识库信息
        $knowledgeNameMap = [];
        if (!empty($allCustomKnowledgeGuids)) {
            $allCustomKnowledgeGuids = array_unique($allCustomKnowledgeGuids);
            $knowledgeList = MerchantKnowledgeBaseModel::getInstance()
                ->whereIn('guid', $allCustomKnowledgeGuids)
                ->field('guid, knowledge_title')
                ->select()
                ->toArray();

            foreach ($knowledgeList as $knowledge) {
                $knowledgeNameMap[$knowledge['guid']] = $knowledge['knowledgeTitle'];
            }
        }

        // 为每个角色添加知识库名称信息
        foreach ($list as &$role) {
            if ($role['knowledgeBindType'] === 'custom' && !empty($role['customKnowledgeGuids'])) {
                $knowledgeNames = [];
                $knowledgeInfo = [];

                foreach ($role['customKnowledgeGuids'] as $guid) {
                    if (isset($knowledgeNameMap[$guid])) {
                        $knowledgeNames[] = $knowledgeNameMap[$guid];
                        $knowledgeInfo[] = [
                            'guid' => $guid,
                            'name' => $knowledgeNameMap[$guid]
                        ];
                    }
                }
                $role['knowledgeBaseInfo'] = $knowledgeInfo;
            } else {
                $role['knowledgeBaseInfo'] = [];
            }
        }

        return $list;
    }

    /**
     * 获取机器人角色详情
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function roleDetail(array $params)
    {
        // 获取角色详情
        $role = MerchantCustomerChatRobotRoleModel::getInstance()
            ->where('guid', $params['roleGuid'])
            ->findOrEmpty();
        if ($role->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '角色不存在');
        }

        // 获取当前用户信息
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 检查商家权限
        if ($userInfo['merchantGuid'] !== $role['merchant_guid']) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您没有权限查看该角色');
        }

        $roleData = $role->toArray();

        // 如果是自定义知识库类型，则查询知识库信息
        if ($role['knowledgeBindType'] === 'custom' && !empty($role['customKnowledgeGuids'])) {
            $knowledgeList = MerchantKnowledgeBaseModel::getInstance()
                ->whereIn('guid', $role['customKnowledgeGuids'])
                ->field('guid, knowledge_title')
                ->select()
                ->toArray();

            $knowledgeNames = [];
            $knowledgeInfo = [];

            foreach ($knowledgeList as $knowledge) {
                $knowledgeInfo[] = [
                    'guid' => $knowledge['guid'],
                    'name' => $knowledge['knowledgeTitle']
                ];
            }
            $roleData['knowledgeBaseInfo'] = $knowledgeInfo;
        } else {
            $roleData['knowledgeBaseInfo'] = [];
        }

        return $roleData;
    }

    /**
     * 创建机器人角色
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function createRole(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_create_role_' . $params['robotGuid'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 3);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        try {
            // 检查机器人是否存在
            $robot = MerchantCustomerChatRobotModel::getInstance()
                ->where('guid', $params['robotGuid'])
                ->findOrEmpty();
            if ($robot->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
            }

            // 获取当前用户信息
            $userId = TokenService::getInstance()->getTokenEntity()->userId;
            $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
            if ($userInfo->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
            }

            // 检查商家权限
            if ($userInfo['merchantGuid'] !== $robot['merchant_guid']) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '您没有权限操作该机器人');
            }

            // 创建角色
            $role = new MerchantCustomerChatRobotRoleModel();
            $role->merchantGuid = $robot['merchant_guid'];
            $role->robotGuid = $params['robotGuid'];
            $role->roleName = $params['roleName'];
            $role->roleDesc = $params['roleDesc'];
            $role->rolePrompt = $params['rolePrompt'];
            $role->knowledgeBindType = $params['knowledgeBindType'];

            // 如果是自定义知识库类型，则需要设置自定义知识库列表
            if ($params['knowledgeBindType'] === 'custom' && !empty($params['customKnowledgeGuids'])) {
                $role->customKnowledgeGuids = $params['customKnowledgeGuids'];
            } else {
                $role->customKnowledgeGuids = [];
            }

            if (!$role->save()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '创建角色失败');
            }

            return [
                'guid' => $role->guid,
                'roleName' => $role->roleName,
            ];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }

    /**
     * 更新机器人角色
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateRole(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_update_role_' . $params['roleGuid'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 3);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        try {
            // 获取角色详情
            $role = MerchantCustomerChatRobotRoleModel::getInstance()
                ->where('guid', $params['roleGuid'])
                ->findOrEmpty();
            if ($role->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '角色不存在');
            }

            // 获取当前用户信息
            $userId = TokenService::getInstance()->getTokenEntity()->userId;
            $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
            if ($userInfo->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
            }

            // 检查商家权限
            if ($userInfo['merchantGuid'] !== $role['merchant_guid']) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '您没有权限操作该角色');
            }

            // 更新角色信息
            $role->roleName = $params['roleName'];
            $role->roleDesc = $params['roleDesc'];
            $role->rolePrompt = $params['rolePrompt'];
            $role->knowledgeBindType = $params['knowledgeBindType'];

            // 如果是自定义知识库类型，则需要设置自定义知识库列表
            if ($params['knowledgeBindType'] === 'custom' && !empty($params['customKnowledgeGuids'])) {
                $role->customKnowledgeGuids = $params['customKnowledgeGuids'];
            } else {
                $role->customKnowledgeGuids = [];
            }

            if (!$role->save()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '更新角色失败');
            }

            return [
                'guid' => $role->guid,
                'roleName' => $role->roleName,
            ];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }

    /**
     * 删除机器人角色
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function deleteRole(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_delete_role_' . $params['roleGuid'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 3);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        try {
            // 获取角色详情
            $role = MerchantCustomerChatRobotRoleModel::getInstance()
                ->where('guid', $params['roleGuid'])
                ->findOrEmpty();
            if ($role->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '角色不存在');
            }

            // 获取当前用户信息
            $userId = TokenService::getInstance()->getTokenEntity()->userId;
            $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
            if ($userInfo->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
            }

            // 检查商家权限
            if ($userInfo['merchantGuid'] !== $role['merchant_guid']) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '您没有权限操作该角色');
            }

            // 删除角色
            if (!$role->delete()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '删除角色失败');
            }

            return [];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }

    /**
     * 提交客服机器人任务
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function submitTask(array $params)
    {
        // 获取缓存锁
        $lockKey = 'customer_chat_robot_submit_task_' . $params['merchantGuid'] . '_' . $params['deviceId'];
        $lock = ZhanhuiCache::getInstance()->lock($lockKey, 3);
        if (!$lock) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '请求过于频繁，请稍后再试');
        }

        try {
            // 验证必要参数
            if (empty($params['deviceId'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '设备ID不能为空');
            }
            if (empty($params['otherPlatformUserUuid'])) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台用户UUID不能为空');
            }

            // 检查机器人是否存在
            $robot = MerchantCustomerChatRobotModel::where([
                'device_id' => $params['deviceId'],
                'robot_type' => $params['robotType'],
                'merchant_guid' => $params['merchantGuid'],
                'other_platform_user_uuid' => $params['otherPlatformUserUuid']
            ])->findOrEmpty();
            if ($robot->isEmpty()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
            }

            // 创建任务
            $task = new MerchantCustomerChatRobotTaskModel();
            $task->merchantGuid = $robot['merchant_guid'];
            $task->robotGuid = $robot->guid;
            $task->taskType = $params['taskType'];
            $task->taskContent = $params['taskContent'];
            $task->taskParams = $params['taskParams'] ?? [];
            $task->resultContent = [];
            $task->taskStatus = 1; // 1-未执行

            if (!$task->save()) {
                throwException(SysErrorCode::SYS_ERROR_CODE, '创建任务失败');
            }

            // 投送队列任务
            $queueData = ['taskGuid' => $task->guid];
            $result = Queue::push(CustomerChatRobotTaskJob::class, $queueData);

            if (!$result) {
                LogError('task', '客服机器人任务投送失败', '队列投送失败', [
                    'taskGuid' => $task->guid,
                    'robotGuid' => $params['robotGuid']
                ]);
            }

            return [
                'guid' => $task->guid,
                'taskType' => $task->taskType,
                'taskStatus' => $task->taskStatus,
            ];
        } catch (\Exception $e) {
            throwException(SysErrorCode::SYS_ERROR_CODE, $e->getMessage());
        } finally {
            // 释放缓存锁
            ZhanhuiCache::getInstance()->unlock($lockKey, $lock);
        }
        return [];
    }

    /**
     * 获取客服机器人任务详情
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTaskDetail(array $params)
    {
        // 验证必要参数
        if (empty($params['taskGuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '任务 GUID 不能为空');
        }

        // 获取任务详情
        $task = MerchantCustomerChatRobotTaskModel::getInstance()
            ->where('guid', $params['taskGuid'])
            ->findOrEmpty();
        if ($task->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '任务不存在');
        }

        return $task->toArray();
    }

    /**
     * 获取客服机器人任务列表
     * @param array $params
     * @return array
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTaskList(array $params)
    {
        // 验证必要参数
        if (empty($params['deviceId'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '设备ID不能为空');
        }
        if (empty($params['otherPlatformUserUuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台用户UUID不能为空');
        }

        // 检查机器人是否存在
        $robot = MerchantCustomerChatRobotModel::where([
            'device_id' => $params['deviceId'],
            'robot_type' => $params['robotType'],
            'merchant_guid' => $params['merchantGuid'],
            'other_platform_user_uuid' => $params['otherPlatformUserUuid']
        ])->findOrEmpty();
        if ($robot->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
        }

        // 构建查询条件
        $query = MerchantCustomerChatRobotTaskModel::getInstance()
            ->where('robot_guid', $params['robotGuid']);

        // 根据任务类型过滤
        if (!empty($params['taskType'])) {
            $query->where('task_type', $params['taskType']);
        }

        // 根据任务状态过滤
        if (!empty($params['taskStatus'])) {
            $query->where('task_status', $params['taskStatus']);
        }

        // 获取任务列表，按创建时间降序排序
        $list = $query->order('create_time', 'desc')
            ->paginate([
                'list_rows' => $params['pageSize'] ?? 10,
                'page' => $params['page'] ?? 1,
            ])
            ->toArray();

        return $list;
    }

    /**
     * 添加指定聊天轮次的待发送记录（内部调用接口）
     * @param array $params
     * @return array
     * @throws ApiException
     */
    public function addSendMessage(array $params)
    {
        // 获取当前用户信息
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 获取商户GUID
        $merchantGuid = $userInfo['merchantGuid'];
        if (empty($merchantGuid)) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户没有关联商户');
        }

        // 验证机器人是否存在
        $robot = MerchantCustomerChatRobotModel::getInstance()
            ->where('guid', $params['robotGuid'])
            ->where('merchant_guid', $merchantGuid)
            ->findOrEmpty();
        if ($robot->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在或无权限操作');
        }

        // 判断聊天轮次是否存在
        $existChatReply = MerchantCustomerChatRobotRecordModel::getInstance()
            ->where('robot_guid', $params['robotGuid'])
            ->where('other_platform_chat_uuid', $params['otherPlatformChatUuid'])
            ->findOrEmpty();
        if ($existChatReply->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '聊天轮次不存在');
        }

        // 创建待发送消息记录
        $message = new MerchantCustomerChatRobotSendMessageModel();
        $message->merchantGuid = $merchantGuid;
        $message->robotGuid = $params['robotGuid'];
        $message->otherPlatformChatUuid = $params['otherPlatformChatUuid'];
        $message->contentType = $params['contentType'];
        $message->content = $params['content'];

        if (!$message->save()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '添加待发送消息失败');
        }

        return [
            'guid' => $message->guid,
            'robotGuid' => $message->robotGuid,
            'contentType' => $message->contentType,
            'createTime' => $message->createTime
        ];
    }

    /**
     * 轮询某个聊天对话轮次有没有待发送的消息（外部设备调用接口）
     * @param array $params
     * @return array
     * @throws ApiException
     */
    public function pollSendMessage(array $params)
    {
        // 验证必要参数
        if (empty($params['merchantGuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '商户GUID不能为空');
        }
        if (empty($params['deviceId'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '设备ID不能为空');
        }
        if (empty($params['robotType'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '机器人类型不能为空');
        }
        if (empty($params['otherPlatformUserUuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台用户UUID不能为空');
        }
        if (empty($params['otherPlatformChatUuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '第三方平台聊天轮次UUID不能为空');
        }

        // 查询机器人
        $robot = MerchantCustomerChatRobotModel::where([
            'device_id' => $params['deviceId'],
            'robot_type' => $params['robotType'],
            'merchant_guid' => $params['merchantGuid'],
            'other_platform_user_uuid' => $params['otherPlatformUserUuid']
        ])->findOrEmpty();

        if ($robot->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '机器人不存在');
        }

        // 查询待发送消息
        $message = MerchantCustomerChatRobotSendMessageModel::where([
            'merchant_guid' => $params['merchantGuid'],
            'robot_guid' => $robot->guid,
            'other_platform_chat_uuid' => $params['otherPlatformChatUuid']
        ])->order('create_time', 'asc')->findOrEmpty();

        if ($message->isEmpty()) {
            // 没有待发送消息
            return [
                'hasMessage' => false
            ];
        }

        // 返回待发送消息
        return [
            'hasMessage' => true,
            'messageGuid' => $message->guid,
            'contentType' => $message->contentType,
            'content' => $message->content,
            'createTime' => $message->createTime
        ];
    }

    /**
     * 删除待发送消息（外部设备完成消息后调用）
     * @param array $params
     * @return array
     * @throws ApiException
     */
    public function deleteSendMessage(array $params)
    {
        // 验证必要参数
        if (empty($params['messageGuid'])) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '消息GUID不能为空');
        }

        // 查询消息
        $message = MerchantCustomerChatRobotSendMessageModel::where('guid', $params['messageGuid'])->findOrEmpty();
        if ($message->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '消息不存在');
        }

        // 删除消息
        if (!$message->delete()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '删除消息失败');
        }

        return [
            'success' => true
        ];
    }
}
