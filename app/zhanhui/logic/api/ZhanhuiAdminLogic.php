<?php

namespace app\zhanhui\logic\api;

use app\admin\logic\admin\AdminUserLogic;
use app\admin\models\AdminUserModel;
use app\admin\models\MerchantHasRolesModel;
use app\admin\models\MerchantRoleHasPermissionsModel;
use app\admin\models\MerchantRoleModel;
use app\constdir\SysErrorCode;
use app\libraries\exception\ApiException;
use app\libraries\service\image_handle\QrcodeService;
use app\libraries\service\token\TokenService;
use think\facade\Db;
use app\user\models\UsersModel;
use app\zhanhui\controller\api\ZhanhuiAdmin;
use app\zhanhui\logic\admin\IndexLogic;
use app\zhanhui\models\MerchantZhanhuiAdminUserModel;
use app\zhanhui\models\MerchantZhanhuiCopyCodeModel;
use app\zhanhui\models\MerchantZhanhuiModel;
use think\helper\Str;

class ZhanhuiAdminLogic
{
    /**
     * 判断是否是展会管理员
     * @param $data
     * @return array
     */
    public function isAdmin($data)
    {
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        if (!empty($data['isSuperCheck']) && $data['isSuperCheck'] == 1) {
            $existAdmin = MerchantZhanhuiAdminUserModel::getInstance()
                ->where('platform_user_sys_id', $userId)
                ->where('is_super_admin', MerchantZhanhuiAdminUserModel::IS_SUPER_ADMIN)
                ->findOrEmpty();
        } else {
            $existAdmin = MerchantZhanhuiAdminUserModel::getInstance()
                ->where('zhanhui_guid', $data['zhanhuiGuid'])
                ->where('platform_user_sys_id', $userId)
                ->findOrEmpty();
        }
        if ($existAdmin->isEmpty()) {
            return [
                'isAdmin' => false,
                'isSuperAdmin' => false
            ];
        }
        return [
            'isAdmin' => true,
            'isSuperAdmin' => $existAdmin->is_super_admin == 1
        ];
    }

    /**
     * 生成展会复制授权码
     * @param $params
     * @return array
     * @throws ApiException
     */
    public function generateCopyCode($params)
    {
        // 获取当前用户ID
        $userId = TokenService::getInstance()->getTokenEntity()->userId;

        // 检查是否是展会管理员
        $existAdmin = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('zhanhui_guid', $params['zhanhuiGuid'])
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();
        if ($existAdmin->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您不是展会管理员，无权生成授权码');
        }

        // 检查展会是否存在
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $params['zhanhuiGuid'])
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        // 生成随机授权码
        $copyCode = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));

        // 检查授权码是否已存在
        $existCode = MerchantZhanhuiCopyCodeModel::getInstance()
            ->where('copy_code', $copyCode)
            ->findOrEmpty();
        if (!$existCode->isEmpty()) {
            // 如果已存在，重新生成
            $copyCode = strtoupper(substr(md5(uniqid(mt_rand(), true)), 0, 8));
        }

        // 设置过期时间，默认120分钟
        $expireTime = time() + 7200;

        // 保存授权码
        $copyCodeModel = new MerchantZhanhuiCopyCodeModel();
        $copyCodeModel->merchantGuid = $zhanhuiInfo->merchantGuid;
        $copyCodeModel->zhanhuiGuid = $params['zhanhuiGuid'];
        $copyCodeModel->copyCode = $copyCode;
        $copyCodeModel->platformUserSysId = $userId;
        $copyCodeModel->status = MerchantZhanhuiCopyCodeModel::STATUS_VALID;
        $copyCodeModel->expireTime = $expireTime;
        $copyCodeModel->save();

        // 生成二维码
        $qrcodeUrl = '';
        try {
            // 生成二维码内容，包含授权码
            $qrcodeContent = 'copyCode=' . $copyCode;

            // 使用二维码生成服务
            $qrcodeService = QrcodeService::getInstance();
            $qrcodeService->setJumpUrl($qrcodeContent);
            $qrcodeService->setReturnType(QrcodeService::RETURN_TYPE_BASE64);
            $qrcodeUrl = $qrcodeService->doCreate();
        } catch (\Exception $e) {
            // 如果生成二维码失败，不影响授权码的生成
            $qrcodeUrl = '';
        }

        return [
            'copyCode' => $copyCode,
            'qrcodeUrl' => $qrcodeUrl,
            'expireTime' => date('Y-m-d H:i:s', $expireTime)
        ];
    }

    /**
     * 验证展会复制授权码
     * @param $params
     * @return array
     * @throws ApiException
     */
    public function verifyCopyCode($params)
    {
        // 获取当前用户ID
        $userId = TokenService::getInstance()->getTokenEntity()->userId;

        // 检查授权码是否存在
        $copyCodeInfo = MerchantZhanhuiCopyCodeModel::getInstance()
            ->where('copy_code', $params['copyCode'])
            ->findOrEmpty();
        if ($copyCodeInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权码不存在或已过期');
        }

        // 检查授权码状态
        if ($copyCodeInfo->status != MerchantZhanhuiCopyCodeModel::STATUS_VALID) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权码已使用或已过期');
        }

        // 检查授权码是否过期
        if ($copyCodeInfo->expireTime < time()) {
            // 更新授权码状态为过期
            $copyCodeInfo->status = MerchantZhanhuiCopyCodeModel::STATUS_EXPIRED;
            $copyCodeInfo->save();
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权码已过期');
        }

        // 获取展会信息
        $zhanhuiInfo = MerchantZhanhuiModel::getInstance()
            ->where('guid', $copyCodeInfo->zhanhuiGuid)
            ->findOrEmpty();
        if ($zhanhuiInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '展会不存在');
        }

        // 获取创建者信息
        $creatorInfo = UsersModel::getInstance()
            ->where('sys_id', $copyCodeInfo->platformUserSysId)
            ->field('nickname,head_imgurl')
            ->findOrEmpty();

        return [
            'valid' => true,
            'zhanhuiInfo' => [
                'guid' => $zhanhuiInfo->guid,
                'name' => $zhanhuiInfo->name,
                'logo' => $zhanhuiInfo->logo,
                'merchantGuid' => $zhanhuiInfo->merchantGuid
            ],
            'creatorInfo' => $creatorInfo->isEmpty() ? [] : $creatorInfo->toArray(),
            'expireTime' => date('Y-m-d H:i:s', $copyCodeInfo->expireTime)
        ];
    }

    /**
     * 使用授权码复制展会
     * @param $params
     * @return array
     * @throws ApiException
     */
    public function copyZhanhui($params)
    {
        // 获取当前用户ID
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 检查授权码是否存在
        $copyCodeInfo = MerchantZhanhuiCopyCodeModel::getInstance()
            ->where('copy_code', $params['copyCode'])
            ->findOrEmpty();
        if ($copyCodeInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权码不存在或已过期');
        }
        $existAdmin = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('zhanhui_guid', $copyCodeInfo->zhanhuiGuid)
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();
        if ($existAdmin->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您不是展会管理员，无权操作');
        }

        // 检查授权码状态
        if ($copyCodeInfo->status != MerchantZhanhuiCopyCodeModel::STATUS_VALID) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权码已使用或已过期');
        }

        // 检查授权码是否过期
        if ($copyCodeInfo->expireTime < time()) {
            // 更新授权码状态为过期
            $copyCodeInfo->status = MerchantZhanhuiCopyCodeModel::STATUS_EXPIRED;
            $copyCodeInfo->save();
            throwException(SysErrorCode::SYS_ERROR_CODE, '授权码已过期');
        }

        // 调用复制展会的方法
        $copyParams = [
            'guid' => $copyCodeInfo->zhanhuiGuid,
            'merchantGuid' => $userInfo->merchantGuid, // 使用当前用户的商户GUID
            'platformUserSysId' => $userId // 平台用户id
        ];

        // 使用管理端的复制逻辑
        $indexLogic = new IndexLogic();
        $result = $indexLogic->copy($copyParams);

        // 更新授权码状态为已使用
        $copyCodeInfo->status = MerchantZhanhuiCopyCodeModel::STATUS_USED;
        $copyCodeInfo->save();

        return $result;
    }

    /**
     * 获取管理账号
     * @param $params
     * @return array|array[]
     * @throws ApiException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAdminAccount($params)
    {
        // 获取当前用户ID
        $userId = TokenService::getInstance()->getTokenEntity()->userId;
        $userInfo = UsersModel::getInstance()->where('sys_id', $userId)->findOrEmpty();
        if ($userInfo->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '用户不存在');
        }

        // 检查是否是展会管理员
        $existAdmin = MerchantZhanhuiAdminUserModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->findOrEmpty();
        if ($existAdmin->isEmpty()) {
            throwException(SysErrorCode::SYS_ERROR_CODE, '您不是展会管理员，无权获取管理账号');
        }

        // 检查是否已绑定管理账号
        $adminUser = AdminUserModel::getInstance()
            ->where('platform_user_sys_id', $userId)
            ->where('merchant_guid', $userInfo->merchantGuid)
            ->findOrEmpty();
        // 使用用户ID生成密码
        // 取前8位作为密码
        $password = substr(md5('zhanhui_' . $userId . '_password'), 0, 8);
        $salt = Str::random(4);
        // 如果账号不存在，自动创建
        if ($adminUser->isEmpty()) {
            try {
                Db::startTrans();
                $defaultRoles = MerchantRoleModel::getInstance()
                    ->where('merchant_guid', $userInfo->merchantGuid)
                    ->where('is_default_for_zhanhui', MerchantRoleModel::IS_DEFAULT_FOR_ZHANHUI)
                    ->where('status', MerchantRoleModel::STATUS_ENABLE)
                    ->select();
                if ($defaultRoles->isEmpty()) {
                    // 创建小程序展会角色
                    $merchantRole = MerchantRoleModel::getInstance();
                    $merchantRole->merchant_guid = $userInfo->merchantGuid;
                    $merchantRole->role_name = '展会角色';
                    $merchantRole->role_intro = '展会角色自动创建';
                    $merchantRole->status = MerchantRoleModel::STATUS_ENABLE;
                    $merchantRole->is_default_for_zhanhui = MerchantRoleModel::IS_DEFAULT_FOR_ZHANHUI;
                    $merchantRole->save();
                    // 保存角色权限关联
                    // 77为展位角色指定权限
                    MerchantRoleHasPermissionsModel::getInstance()->insertAll([[
                        'merchant_guid' => $userInfo->merchant_guid,
                        'merchant_role_id' => $merchantRole->sys_id,
                        'permission_id' => 77,
                        'create_time' => time(),
                        'update_time' => time()
                    ]]);
                    $defaultRoles = [$merchantRole];
                }

                // 生成随机用户名
                $randomStr = substr(md5('zhanhui_' . $userId . '_name'), 0, 6);
                $userName = 'zhanhui_' . $randomStr;

                // 创建管理账号
                $adminUser = new AdminUserModel();
                $adminUser->userName = $userName; // 随机生成的用户名
                $adminUser->nickname = $userInfo->nickname; // 昵称使用用户昵称
                $adminUser->mobile = $userInfo->mobile; // 手机号使用用户手机号
                $adminUser->password = $this->generateHashPassword($password, $salt); // 使用用户ID生成的密码
                $adminUser->salt = $salt; // 目前不使用盐
                $adminUser->state = AdminUserModel::STATE_ENABLE; // 状态为启用
                $adminUser->adminType = 'merchant'; // 管理员类型为商户
                $adminUser->merchantGuid = $userInfo->merchantGuid; // 商户GUID
                $adminUser->platformUserSysId = $userId; // 绑定的平台用户ID
                $adminUser->save();
                foreach ($defaultRoles as $role) {
                    // 分配角色
                    $merchantHasRoles = new MerchantHasRolesModel();
                    $merchantHasRoles->merchantGuid = $userInfo->merchantGuid;
                    $merchantHasRoles->adminUserId = $adminUser->sysId;
                    $merchantHasRoles->merchantRoleId = $role->sysId;
                    $merchantHasRoles->save();
                }
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                throwException(SysErrorCode::SYS_ERROR_CODE, '网络繁忙，请稍后再试！');
            }
        }

        // 返回管理账号信息
        $result = [
            'adminUser' => [
                'guid' => $adminUser->guid,
                'userName' => $adminUser->userName,
                'nickname' => $adminUser->nickname,
                'mobile' => $adminUser->mobile,
                'state' => $adminUser->state
            ]
        ];

        //// 如果是新创建的账号，返回密码
        //if (isset($password)) {
        //    $result['adminUser']['password'] = $password;
        //    $result['adminUser']['isNewAccount'] = true;
        //} else {
        //    $result['adminUser']['isNewAccount'] = false;
        //}
        $result['adminUser']['password'] = $password;
        $result['adminUser']['isNewAccount'] = true;
        return $result;
    }

    //加密密码
    protected function generateHashPassword(string $password, string $salt): string
    {
        return md5(sha1($password) . $salt);
    }
}
