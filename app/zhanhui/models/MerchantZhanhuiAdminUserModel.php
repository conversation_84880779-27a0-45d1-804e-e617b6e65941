<?php

declare(strict_types=1);

namespace app\zhanhui\models;

use app\libraries\models\BaseModel;
use app\user\models\UsersModel;

/**
 * @property int $sysId 主键字段
 * @property string $guid 唯一关键字段
 * @property string $merchantGuid 商家guid
 * @property string $zhanhuiGuid 展会guid
 * @property int $platformUserSysId 用户id
 * @property int $isSuperAdmin 是否为超级管理员：0-否；1-是
 * @property int $createTime 创建时间戳
 * @property int $updateTime 修改时间戳
 * @property int $deletedAt 删除时间
 * @property string $modifyTime 维护字段-更新时间
 * @mixin BaseModel
 */
class MerchantZhanhuiAdminUserModel extends BaseModel
{
    /**
    * @var string
    */
    protected $name = 'merchant_zhanhui_admin_user';

    protected static bool $isGuid = true;

    // 是否为超级管理员
    public const IS_SUPER_ADMIN = 1; // 是
    public const IS_NOT_SUPER_ADMIN = 0; // 否


    public function user()
    {
        return $this->hasOne(UsersModel::class, 'sys_id', 'platform_user_sys_id')
            ->field('sys_id,guid,nickname,head_imgurl');
    }
}
